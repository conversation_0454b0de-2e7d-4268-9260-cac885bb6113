import "dotenv/config";
import { mastra } from "./src/mastra/index.js";

async function main() {
  try {
    console.log("Testing Dictionary Agent...");
    
    const agent = await mastra.getAgent("dictionaryAgent");
    
    if (!agent) {
      console.error("Dictionary agent not found!");
      return;
    }
    
    console.log("Agent found, testing with word 'hello'...");
    
    const result = await agent.generate("What does the word 'hello' mean?");
    
    console.log("Agent response:", result.text);
  } catch (error) {
    console.error("Error:", error);
  }
}

main();
