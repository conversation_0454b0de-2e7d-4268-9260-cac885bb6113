import "dotenv/config";
import express from "express";
import { mastra } from "./src/mastra/index.js";

const app = express();
const port = 3000;

app.use(express.json());

// Test endpoint
app.get("/", (req, res) => {
  res.json({ message: "Dictionary Agent Server is running!" });
});

// Agent endpoint
app.post("/chat", async (req, res) => {
  try {
    const { message } = req.body;
    
    if (!message) {
      return res.status(400).json({ error: "Message is required" });
    }

    console.log("Getting agent...");
    const agent = await mastra.getAgent("dictionaryAgent");
    
    if (!agent) {
      return res.status(500).json({ error: "Dictionary agent not found!" });
    }

    console.log("Generating response for:", message);
    const result = await agent.generate(message);
    
    res.json({ 
      response: result.text,
      message: message 
    });
  } catch (error) {
    console.error("Error:", error);
    res.status(500).json({ error: error.message });
  }
});

app.listen(port, () => {
  console.log(`Dictionary Agent Server running at http://localhost:${port}`);
  console.log("Test with: curl -X POST http://localhost:3000/chat -H 'Content-Type: application/json' -d '{\"message\":\"What does hello mean?\"}'");
});
