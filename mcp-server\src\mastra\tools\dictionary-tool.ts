import { createTool } from "@mastra/core/tools";
import { z } from "zod";

export const dictionaryTool = createTool({
  id: "get-word-definition",
  description: "Get definitions for a word using the dictionary API",
  inputSchema: z.object({
    word: z.string().describe("The word to get definitions for"),
  }),
  outputSchema: z.object({
    definitions: z.string().describe("The definitions of the word"),
    word: z.string().describe("The word that was looked up"),
  }),
  execute: async ({ context }) => {
    const { word } = context;
    
    try {
      // Use the dictionary API directly
      const url = `https://api.dictionaryapi.dev/api/v2/entries/en/${word}`;
      const response = await fetch(url);
      
      if (response.status === 200) {
        const data = await response.json();
        const definitions: string[] = [];
        
        for (const meaning of data[0]['meanings']) {
          for (const definition of meaning['definitions']) {
            definitions.push(definition['definition']);
          }
        }
        
        return {
          definitions: definitions.join('\n'),
          word: word,
        };
      } else {
        return {
          definitions: "No definitions found.",
          word: word,
        };
      }
    } catch (error) {
      return {
        definitions: `Error fetching definitions: ${error}`,
        word: word,
      };
    }
  },
});
