import { <PERSON><PERSON> } from '@mastra/core';
import { PinoLogger } from "@mastra/loggers";
import { LibSQLStore } from "@mastra/libsql";
import { dictionaryAgent } from "./agents/dictionary-agent.js";

export const mastra = new Mastra({
  agents: {
    dictionaryAgent
  },
  storage: new LibSQLStore({
    // stores telemetry, evals, ... into memory storage, if it needs to persist, change to file:../mastra.db
    url: ":memory:",
  }),
  logger: new PinoLogger({
    name: "Mastra Dictionary Agent",
    level: "info",
  }),
});
